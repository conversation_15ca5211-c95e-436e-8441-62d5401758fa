<?php
/**
 * تخصيص واجهة Freemius
 *
 * @package Pexlat_Form
 */

if (!defined('WPINC')) {
    die;
}

/**
 * تخصيص صفحة التفعيل لإظهار حقل إدخال الترخيص فقط
 */
function pexlat_form_customize_connect_message($message, $user_first_name, $plugin_title, $user_login, $site_link, $freemius_link) {
    return sprintf(
        'أدخل مفتاح الترخيص الخاص بك',
        '<b>' . $plugin_title . '</b>'
    );
}

/**
 * تحميل ملف CSS المخصص لـ Freemius
 */
function pexlat_form_enqueue_freemius_styles() {
    wp_enqueue_style(
        'pexlat-form-freemius-custom',
        plugin_dir_url(dirname(__FILE__)) . 'admin/css/freemius-custom.css',
        array(),
        PEXLAT_FORM_VERSION
    );
}

/**
 * إخفاء العناصر غير المطلوبة في صفحة التفعيل
 */
function pexlat_form_customize_connect_page() {
    // تحميل ملف CSS المخصص
    pexlat_form_enqueue_freemius_styles();

    // تغيير عنوان الصفحة
    add_filter('fs_connect_page_title', 'pexlat_form_customize_connect_page_title');
}

/**
 * تخصيص عنوان صفحة التفعيل
 */
function pexlat_form_customize_connect_page_title($title) {
    return 'تفعيل ترخيص الإضافة';
}

/**
 * التحقق من صلاحية الترخيص قبل تشغيل الإضافة
 */
function pexlat_form_check_license() {
    // التحقق من وجود Freemius
    if (!function_exists('fe_fs')) {
        return true; // السماح بالتشغيل إذا لم يتم تحميل Freemius
    }

    $fs = fe_fs();

    // التحقق من أن fe_fs() أعاد كائن Freemius وليس false
    if ($fs === false) {
        // إضافة إشعار للمسؤول
        add_action('admin_notices', 'pexlat_form_freemius_missing_notice');

        // السماح بالتشغيل في حالة عدم وجود Freemius
        return true;
    }

    // السماح بالتشغيل في صفحة التفعيل
    if (isset($_GET['page']) && $_GET['page'] === 'pexlat-form-pricing') {
        return true;
    }

    // التحقق من وجود ترخيص صالح
    if (!$fs->is_registered()) {
        // إضافة إشعار للمسؤول
        add_action('admin_notices', 'pexlat_form_license_notice');

        // تعطيل وظائف الإضافة
        return false;
    }

    // التحقق من أن الترخيص مدفوع
    if (!$fs->is_paying()) {
        // إضافة إشعار للمسؤول
        add_action('admin_notices', 'pexlat_form_license_not_paying_notice');

        // تعطيل وظائف الإضافة
        return false;
    }

    // تجاهل حالة "انتظار التفعيل" والسماح بالتشغيل
    // (تم تعطيل التحقق من الإيميل حسب تفضيلات المستخدم)
    if ($fs->is_pending_activation()) {
        // السماح بالتشغيل حتى لو كان في انتظار التفعيل
        // لأن التحقق من الإيميل لا يعمل بشكل جيد
        // لا يتم عرض أي إشعار (تم إخفاؤه حسب طلب المستخدم)
    }

    // التحقق من عدد المواقع المسموح بها
    $license = $fs->_get_license();
    if ($license) {
        // التحقق من انتهاء صلاحية الترخيص
        if ($license->is_expired()) {
            // إضافة إشعار للمسؤول
            add_action('admin_notices', 'pexlat_form_license_expired_notice');

            // تعطيل وظائف الإضافة
            return false;
        }

        // التحقق من استخدام الترخيص بالكامل
        if ($license->is_utilized()) {
            // إضافة إشعار للمسؤول بأن الترخيص مستخدم بالكامل
            add_action('admin_notices', 'pexlat_form_license_utilized_notice');

            // تعطيل وظائف الإضافة
            return false;
        }
    }

    return true;
}

/**
 * إشعار للمسؤول عند عدم وجود ترخيص صالح
 */
function pexlat_form_license_notice() {
    ?>
    <div class="error">
        <p><?php echo 'يرجى تفعيل الإضافة للاستفادة من جميع الميزات.'; ?> <a href="<?php echo admin_url('plugins.php?page=pexlat-form'); ?>"></a></p>
    </div>
    <?php
}

/**
 * إشعار للمسؤول عند عدم وجود ترخيص مدفوع
 */
function pexlat_form_license_not_paying_notice() {
    ?>
    <div class="error">
        <p><?php echo 'يجب أن يكون لديك ترخيص مدفوع للإضافة للاستفادة من جميع الميزات.'; ?> <a href="<?php echo admin_url('plugins.php?page=pexlat-form-pricing'); ?>"></a></p>
    </div>
    <?php
}

/**
 * إشعار للمسؤول عند وجود ترخيص في انتظار التفعيل
 */
function pexlat_form_license_pending_notice() {
    ?>
    <div class="error">
        <p><?php echo 'الترخيص في انتظار التفعيل للاستفادة من جميع الميزات.'; ?> <a href="<?php echo admin_url('plugins.php?page=pexlat-form'); ?>"></a></p>
    </div>
    <?php
}

/**
 * إشعار للمسؤول عند انتهاء صلاحية الترخيص
 */
function pexlat_form_license_expired_notice() {
    ?>
    <div class="error">
        <p><?php echo 'الترخيص منتهي الصلاحية للاستفادة من جميع الميزات.'; ?> <a href="<?php echo admin_url('plugins.php?page=pexlat-form'); ?>"></a></p>
    </div>
    <?php
}

/**
 * إشعار للمسؤول عند استخدام الترخيص على عدد أكبر من المواقع المسموح بها
 */
function pexlat_form_license_utilized_notice() {
    ?>
    <div class="error">
        <p><?php echo 'الترخيص مستخدم بالكامل على العدد المسموح به من المواقع.'; ?> <a href="<?php echo admin_url('plugins.php?page=pexlat-form'); ?>"></a></p>
    </div>
    <?php
}

/**
 * إشعار للمسؤول عند عدم وجود Freemius
 */
function pexlat_form_freemius_missing_notice() {
    ?>
    <div class="error">
        <p><?php echo 'لم يتم العثور على SDK. يرجى التأكد من وجود المجلد المطلوب في مجلد الإضافة.'; ?></p>
    </div>
    <?php
}

/**
 * تحميل ملف CSS المخصص في لوحة التحكم
 */
function pexlat_form_admin_enqueue_scripts() {
    // تحميل ملف CSS المخصص في صفحات الإضافة
    if (isset($_GET['page']) && strpos($_GET['page'], 'pexlat-form') !== false) {
        pexlat_form_enqueue_freemius_styles();
    }
}

// تسجيل الدوال مع Freemius
add_filter('fs_connect_message_on_update', 'pexlat_form_customize_connect_message', 10, 6);
add_filter('fs_connect_message', 'pexlat_form_customize_connect_message', 10, 6);
add_action('fs_connect_before_render_connect_page', 'pexlat_form_customize_connect_page');
add_action('admin_enqueue_scripts', 'pexlat_form_admin_enqueue_scripts');
