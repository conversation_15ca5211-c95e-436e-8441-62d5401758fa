<?php

/**
 * Pexlat Form
 *
 * @link              https://pexlat.com
 * @since             1.0.3
 * @package           Pexlat_Form
 *
 * @wordpress-plugin
 * Plugin Name:       Pexlat Form
 * Plugin URI:        https://pexlat.com/plugins/pexlat-form
 * Description:       إضافة لإنشاء نماذج مخصصة لجمع معلومات العملاء عند طلب المنتجات في ووكومرس
 * Version:           1.5.0
 * Update URI: https://api.freemius.com
 * Author:            Pexlat
 * Author URI:        https://pexlat.com
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       pexlat-form
 * Domain Path:       /languages
 */
// If this file is called directly, abort.
if ( !defined( 'WPINC' ) ) {
    die;
}
/**
 * Current plugin version.
 */
define( 'PEXLAT_FORM_VERSION', '1.5.0' );

/**
 * Plugin directory path.
 */
define( 'PEXLAT_FORM_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
/**
 * تكامل Freemius
 */
if ( !function_exists( 'fe_fs' ) ) {
    // إنشاء دالة مساعدة للوصول السهل إلى SDK
    function fe_fs() {
        global $fe_fs;
        if ( !isset( $fe_fs ) ) {
            // التحقق من وجود مجلد freemius
            $freemius_sdk_path = dirname( __FILE__ ) . '/freemius/start.php';
            // إذا لم يكن مجلد freemius موجودًا، لا تقم بتحميل SDK
            if ( !file_exists( $freemius_sdk_path ) ) {
                return false;
            }
            // تضمين Freemius SDK
            require_once $freemius_sdk_path;
            $fe_fs = fs_dynamic_init( array(
                'id'               => '18921',
                'slug'             => 'pexlat-form',
                'type'             => 'plugin',
                'public_key'       => 'pk_71c61c96e2bfdcca2fed176b70b3b',
                'is_premium'       => true,
                'is_premium_only'  => true,
                'has_addons'       => false,
                'has_paid_plans'   => true,
                'is_org_compliant' => false,
                'menu'             => array(
                    'first-path' => 'plugins.php',
                    'support'    => false,
                ),
                'is_live'          => true,
            ) );
        }
        return $fe_fs;
    }

    // تهيئة Freemius
    fe_fs();
    // إشارة إلى أن SDK تم تحميله
    do_action( 'fe_fs_loaded' );
    // إضافة إجراء التنظيف بعد إلغاء التثبيت
    fe_fs()->add_action( 'after_uninstall', 'fe_fs_uninstall_cleanup' );
}
/**
 * The code that runs during plugin activation.
 */
function activate_pexlat_form() {
    require_once plugin_dir_path( __FILE__ ) . 'includes/class-pexlat-form-activator.php';
    Pexlat_Form_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_pexlat_form() {
    require_once plugin_dir_path( __FILE__ ) . 'includes/class-pexlat-form-deactivator.php';
    Pexlat_Form_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_pexlat_form' );
register_deactivation_hook( __FILE__, 'deactivate_pexlat_form' );
/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-pexlat-form.php';
/**
 * الجزائر بيانات المدن والبلديات
 */
require plugin_dir_path( __FILE__ ) . 'includes/algeria-cities.php';
/**
 * The helper class with utility functions.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-pexlat-form-helper.php';
/**
 * ميتا بوكس المتغيرات والإعدادات في صفحة المنتج
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-product-variation-metabox.php';

/**
 * ميتا بوكس تعطيل نموذج الطلب للمنتج
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-form-disable-metabox.php';

/**
 * ميتا بوكس إعدادات السلة للمنتج
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-cart-metabox.php';



/**
 * نظام السلة المخصص
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-custom-cart-system.php';




/**
 * تخصيص Freemius وإدارة التراخيص
 */
require plugin_dir_path( __FILE__ ) . 'includes/freemius-customization.php';

/**
 * ملف اختبار حلول الأمان (فقط في بيئة التطوير)
 */
if (defined('WP_DEBUG') && WP_DEBUG) {
    require plugin_dir_path( __FILE__ ) . 'test-security-fix.php';
}

/**
 * مدير تحديث فئات الشحن
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-shipping-classes-updater.php';


/**
 * Check for WooCommerce and license before initializing the plugin
 */
function pexlat_form_check_woocommerce() {
    if ( !class_exists( 'WooCommerce' ) ) {
        add_action( 'admin_notices', 'pexlat_form_woocommerce_notice' );
        return;
    }
    // التحقق من صلاحية الترخيص
    if ( function_exists( 'pexlat_form_check_license' ) && !pexlat_form_check_license() ) {
        // لا تقم بتشغيل الإضافة إذا كان الترخيص غير صالح
        return;
    }
    /**
     * Begins execution of the plugin.
     *
     * @since    1.0.0
     */
    function run_pexlat_form() {
        $plugin = new Pexlat_Form();
        $plugin->run();
    }

    run_pexlat_form();
}

add_action( 'plugins_loaded', 'pexlat_form_check_woocommerce' );
/**
 * Notice for WooCommerce requirement
 */
function pexlat_form_woocommerce_notice() {
    ?>
    <div class="error">
        <p><?php
    // Use direct text instead of translation functions to avoid early loading
    echo 'Pexlat Form requires WooCommerce to be installed and activated.';
    ?></p>
    </div>
    <?php
}

/**
 * دالة لحذف مجلد وكل محتوياته بشكل متكرر
 *
 * @param string $dir مسار المجلد المراد حذفه
 * @return bool نجاح أو فشل العملية
 */
function pexlat_form_delete_directory( $dir ) {
    if ( !file_exists( $dir ) ) {
        return true;
    }

    if ( !is_dir( $dir ) ) {
        return unlink( $dir );
    }

    foreach ( scandir( $dir ) as $item ) {
        if ( $item == '.' || $item == '..' ) {
            continue;
        }

        if ( !pexlat_form_delete_directory( $dir . DIRECTORY_SEPARATOR . $item ) ) {
            return false;
        }
    }

    return rmdir( $dir );
}

/**
 * دالة التنظيف بعد إلغاء تثبيت الإضافة
 * تُستدعى بواسطة Freemius بعد إرسال بيانات إلغاء التثبيت إلى الخادم
 */
function fe_fs_uninstall_cleanup() {
    // التحقق مما إذا كان يجب حذف البيانات
    $delete_data = get_option( 'pexlat_form_delete_data' );
    if ( $delete_data ) {
        global $wpdb;

        // تنظيف مناطق الشحن الجزائرية أولاً
        pexlat_form_cleanup_shipping_zones_on_uninstall();

        // حذف الجداول
        $wpdb->query( "DROP TABLE IF EXISTS {$wpdb->prefix}pexlat_form_forms" );
        $wpdb->query( "DROP TABLE IF EXISTS {$wpdb->prefix}pexlat_form_submissions" );

        // حذف جميع خيارات الإضافة
        $options_to_delete = array(
            // خيارات أساسية
            'pexlat_form_version',
            'pexlat_form_default_form_id',
            'pexlat_form_delete_data',
            'pexlat_form_shipping_companies_data',
            'pexlat_form_deleted_companies',
            'pexlat_form_notification_email',
            'pexlat_form_container_style',
            'pexlat_form_settings',
            'pexlat_form_woo_location',

            // خيارات النموذج
            'pexlat_form_title_text',
            'pexlat_form_description_text',
            'pexlat_form_require_fields',
            'pexlat_form_language',
            'pexlat_form_send_customer_email',

            // خيارات التحقق من رقم الهاتف
            'pexlat_form_phone_validation_enabled',
            'pexlat_form_phone_prefixes',
            'pexlat_form_phone_length',
            'pexlat_form_custom_phone_validation',

            // خيارات تقييد الطلبات
            'pexlat_form_limit_orders_enabled',
            'pexlat_form_max_orders',
            'pexlat_form_time_period',

            // خيارات متقدمة
            'pexlat_form_disable_autocomplete',
            'pexlat_form_disable_copy_paste',
            'pexlat_form_save_abandoned_orders',
            'pexlat_form_abandoned_order_status',

            // خيارات إضافية
            'shipping_companies_data',
            'pexlat_form_shipping_class_costs',
            'fs_debug_mode',
            'fs_storage_logger',
            'fs_active_plugins'
        );
        
        // حذف خيارات WooCommerce المرتبطة بالإضافة
        // استعادة إعدادات WooCommerce الافتراضية إذا تم تعديلها بواسطة الإضافة
        if (function_exists('WC')) {
            // استرجاع جميع معرفات مناطق الشحن المسطحة التي تم إنشاؤها بواسطة الإضافة
            $shipping_zones = WC_Shipping_Zones::get_zones();
            foreach ($shipping_zones as $zone_data) {
                $zone = new WC_Shipping_Zone($zone_data['id']);
                $shipping_methods = $zone->get_shipping_methods();
                foreach ($shipping_methods as $instance_id => $method) {
                    if ($method->id === 'flat_rate') {
                        delete_option('woocommerce_flat_rate_' . $instance_id . '_settings');
                    }
                }
            }
        }
        
        // حذف جميع الخيارات
        foreach ( $options_to_delete as $option ) {
            delete_option( $option );
        }
        
        // حذف الكاش المخزن
        wp_cache_flush();
        
        // حذف البيانات المؤقتة
        $upload_dir = wp_upload_dir();
        $pexlat_form_dir = $upload_dir['basedir'] . '/pexlat-form';
        if ( file_exists( $pexlat_form_dir ) && is_dir( $pexlat_form_dir ) ) {
            pexlat_form_delete_directory( $pexlat_form_dir );
        }

        // إذا كان الموقع متعدد، قم بحذف البيانات لجميع المواقع
        if ( is_multisite() ) {
            $sites = get_sites();
            foreach ( $sites as $site ) {
                switch_to_blog( $site->blog_id );
                
                // حذف جميع الخيارات لكل موقع
                foreach ( $options_to_delete as $option ) {
                    delete_option( $option );
                }
                
                // حذف الكاش المخزن لكل موقع
                wp_cache_flush();
                
                restore_current_blog();
            }
        }
        
        // حذف البيانات المخزنة في جدول الخيارات المؤقتة
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_pexlat_form_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_timeout_pexlat_form_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_orders_history_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_timeout_orders_history_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_fs_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '%_transient_timeout_fs_%'" );

        // حذف البيانات المخزنة في جدول postmeta
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE '_pexlat_form_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key = '_form_settings'" );

        // حذف رسوم التوصيل الإضافية من جميع المنتجات
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key = '_pexlat_form_additional_shipping_cost'" );
        $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key = '_pexlat_form_shipping_cost_description'" );
        
        // حذف أي طلبات مسودة تم إنشاؤها بواسطة الإضافة
        $draft_orders = $wpdb->get_col(
            "SELECT ID FROM {$wpdb->posts}
            WHERE post_type = 'shop_order'
            AND post_status = 'draft'
            AND EXISTS (SELECT * FROM {$wpdb->postmeta}
                       WHERE post_id = {$wpdb->posts}.ID
                       AND meta_key = '_pexlat_form_form_data')"
        );
        
        if (!empty($draft_orders)) {
            foreach ($draft_orders as $order_id) {
                wp_delete_post($order_id, true);
            }
        }
    }
}

/**
 * تنظيف مناطق الشحن الجزائرية عند إلغاء التثبيت
 */
function pexlat_form_cleanup_shipping_zones_on_uninstall() {
    if (!class_exists('WC_Shipping_Zones')) {
        return;
    }

    global $wpdb;

    try {
        // الحصول على جميع معرفات طرق الشحن المرتبطة بالولايات الجزائرية
        $shipping_method_ids = $wpdb->get_col("
            SELECT DISTINCT szm.instance_id
            FROM {$wpdb->prefix}woocommerce_shipping_zone_methods szm
            INNER JOIN {$wpdb->prefix}woocommerce_shipping_zone_locations szl
            ON szm.zone_id = szl.zone_id
            WHERE szl.location_code LIKE 'DZ:DZ-%'
        ");

        // حذف مناطق الشحن الجزائرية
        $zone_ids = $wpdb->get_col("
            SELECT DISTINCT zone_id
            FROM {$wpdb->prefix}woocommerce_shipping_zone_locations
            WHERE location_code LIKE 'DZ:DZ-%'
        ");

        foreach ($zone_ids as $zone_id) {
            $zone = new WC_Shipping_Zone($zone_id);
            $zone->delete();
        }

        // تنظيف خيارات طرق الشحن
        foreach ($shipping_method_ids as $instance_id) {
            delete_option('woocommerce_flat_rate_' . $instance_id . '_settings');
        }

        // حذف جميع البيانات الوصفية المتعلقة برسوم التوصيل الإضافية
        $wpdb->query("
            DELETE FROM {$wpdb->postmeta}
            WHERE meta_key IN (
                '_pexlat_form_additional_shipping_cost',
                '_pexlat_form_shipping_cost_description'
            )
        ");

        // تنظيف أي بيانات متبقية
        $wpdb->query("
            DELETE FROM {$wpdb->prefix}woocommerce_shipping_zone_locations
            WHERE location_code LIKE 'DZ:DZ-%'
        ");

        // إعادة تعيين إعدادات الدول المسموحة
        delete_option('woocommerce_allowed_countries');
        delete_option('woocommerce_specific_allowed_countries');

    } catch (Exception $e) {
        error_log('Pexlat Form: خطأ في تنظيف مناطق الشحن عند إلغاء التثبيت - ' . $e->getMessage());
    }
}
