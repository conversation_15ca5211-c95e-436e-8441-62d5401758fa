# إصلاح مشكلة تحميل الترجمات المبكر

## المشكلة
كانت الإضافة تواجه خطأ في تحميل الترجمات مبكراً جداً قبل hook الـ `init`، مما يسبب الخطأ التالي:

```
PHP Notice: Function _load_textdomain_just_in_time was called incorrectly. 
Translation loading for the 'pexlat-form' domain was triggered too early. 
This is usually an indicator for some code in the plugin or theme running too early. 
Translations should be loaded at the 'init' action or later.
```

## الحلول المطبقة

### 1. تغيير hook تحميل الترجمات
- تم تغيير hook تحميل الترجمات من `plugins_loaded` إلى `init`
- الملف: `includes/class-pexlat-form.php` - السطر 131

### 2. إضافة حماية في دالة تحميل الترجمات
- تم إضافة فحص `did_action('init')` قبل تحميل الترجمات
- الملف: `includes/class-pexlat-form-i18n.php` - السطر 40

### 3. إزالة استدعاءات الترجمة المبكرة
- تم استبدال جميع استدعاءات `_e()` و `__()` في ملف `freemius-customization.php` بنصوص مباشرة
- تم استبدال `esc_html__()` في دالة إشعار WooCommerce بنص مباشر

## الملفات المعدلة

1. **includes/class-pexlat-form.php**
   - تغيير hook من `plugins_loaded` إلى `init`

2. **includes/class-pexlat-form-i18n.php**
   - إضافة فحص `did_action('init')`

3. **includes/freemius-customization.php**
   - إزالة جميع استدعاءات دوال الترجمة

4. **pexlat-form.php**
   - إزالة استدعاء `esc_html__()` في دالة الإشعار

## ملاحظات مهمة

### بخصوص خطأ backup-backup
الخطأ المذكور في رسالة المستخدم يشير إلى domain `backup-backup` وليس `pexlat-form`. هذا يعني أن:

1. المشكلة الأساسية في إضافة أخرى تسمى "backup-backup"
2. الحلول المطبقة هنا تضمن عدم حدوث نفس المشكلة في إضافة Pexlat Form
3. لحل مشكلة backup-backup، يجب:
   - تحديث إضافة backup-backup إلى أحدث إصدار
   - أو تعطيلها مؤقتاً لاختبار الموقع
   - أو التواصل مع مطور الإضافة

## التحقق من الإصلاح

للتأكد من أن الإصلاح يعمل:

1. قم بتفعيل الإضافة على موقع اختبار
2. تحقق من عدم ظهور أخطاء PHP في ملف error_log
3. تأكد من عمل الترجمات بشكل صحيح
4. اختبر الإضافة في بيئات مختلفة

## الوقاية المستقبلية

- تجنب استدعاء دوال الترجمة في الكود الذي يتم تنفيذه قبل hook الـ `init`
- استخدام `did_action('init')` للتحقق من جاهزية WordPress
- تأجيل تحميل الترجمات حتى الوقت المناسب
